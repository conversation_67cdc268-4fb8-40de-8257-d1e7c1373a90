import { User } from 'lucide-react';
import useDataFetching from '@/hooks/useDataFetching';
import { useState, useEffect } from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import api from '@/lib/axios';
import InputField from '@/components/ui/InputField';
import { useTranslation } from "react-i18next";
import { API_URL } from '@/config';
import { useQueryClient } from '@tanstack/react-query';

export default function Profile() {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { data: userInfo } = useDataFetching({
    queryKey: 'user',
    endPoint: 'profile',
  });

  const [selectedImage, setSelectedImage] = useState(null);
  const [previewImage, setPreviewImage] = useState(null);

  useEffect(() => {
    if (userInfo?.data?.profile_picture_url) {
      setPreviewImage(userInfo.data.profile_picture_url);
    }
  }, [userInfo]);

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setSelectedImage(file);
      setPreviewImage(URL.createObjectURL(file));
    }
  };

  const validationSchema = Yup.object({
    name: Yup.string().required(t("profile.validation.nameRequired")),
    mobile: Yup.string(),
    bio: Yup.string(),
    present_address: Yup.string(),
    current_password: Yup.string(),
    new_password: Yup.string().when('current_password', {
      is: (val) => val && val.length > 0,
      then: (schema) => schema.min(6, t("profile.validation.passwordMin")).required(t("profile.validation.newPasswordRequired")),
      otherwise: (schema) => schema.notRequired(),
    }),
    confirm_password: Yup.string().when('new_password', {
      is: (val) => val && val.length > 0,
      then: (schema) => schema
        .oneOf([Yup.ref('new_password'), null], t("profile.validation.passwordMismatch"))
        .required(t("profile.validation.confirmPasswordRequired")),
      otherwise: (schema) => schema.notRequired(),
    }),
  });

  const handleFormSubmit = async (values, { setSubmitting }) => {
    const formData = new FormData();
    formData.append('name', values.name);
    formData.append('mobile', values.mobile);
    formData.append('bio', values.bio);
    formData.append('present_address', values.present_address);
    formData.append('current_password', values.current_password);
    formData.append('new_password', values.new_password);

    if (selectedImage) {
      formData.append('profile_picture', selectedImage);
    }

    try {
      const response = await api.post(
        `${API_URL}/profile/update`,
        formData,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      if (response.status === 200) {
        console.log(t("profile.updateSuccess"));
        if (selectedImage) {
          setPreviewImage(URL.createObjectURL(selectedImage));
        }

        // Invalidate user query to refresh data across the app
        queryClient.invalidateQueries({ queryKey: ['user'] });
      } else {
        console.log(t("profile.updateFail"));
      }
    } catch (error) {
      console.error(t("profile.updateError"), error);
    }
    setSubmitting(false);
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <h2 className="text-2xl font-bold text-gray-800 dark:text-white">{t("profile.title")}</h2>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <div className="flex items-center space-x-4 mb-6">
          <div className="relative w-20 h-20 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center overflow-hidden cursor-pointer">
            {previewImage ? (
              <img
                src={previewImage}
                alt={t("profile.profilePicture")}
                className="w-full h-full object-cover rounded-full"
              />
            ) : (
              <User className="w-10 h-10 text-gray-400" />
            )}
            <input
              type="file"
              accept="image/*"
              onChange={handleImageChange}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              title={t("profile.uploadProfilePicture")}
            />
          </div>
        </div>

        <Formik
          initialValues={{
            name: userInfo?.data?.name || '',
            mobile: userInfo?.data?.mobile || '',
            bio: userInfo?.data?.bio || '',
            present_address: userInfo?.data?.present_address || '',
            current_password: '',
            new_password: '',
            confirm_password: '',
          }}
          enableReinitialize
          validationSchema={validationSchema}
          onSubmit={handleFormSubmit}
        >
          {({ isSubmitting }) => (
            <Form className="space-y-6">
              <InputField label={t("profile.fullName")} name="name" type="text" required />
              <InputField label={t("profile.mobile")} name="mobile" type="text" />
              <InputField label={t("profile.bio")} name="bio" type="text" component="textarea" />
              <InputField label={t("profile.address")} name="present_address" type="text" />
              <InputField label={t("profile.currentPassword")} name="current_password" type="password" />
              <InputField label={t("profile.newPassword")} name="new_password" type="password" />
              <InputField label={t("profile.confirmNewPassword")} name="confirm_password" type="password" />
              <button type="submit" disabled={isSubmitting} className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                {isSubmitting ? t("profile.saving") : t("profile.saveChanges")}
              </button>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
}
